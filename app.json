{"expo": {"name": "<PERSON><PERSON>", "slug": "rabid-app", "version": "1.1.6", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "rabid", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"usesAppleSignIn": true, "supportsTablet": true, "bundleIdentifier": "rabid.app", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSUserTrackingUsageDescription": "This identifier will be used to deliver personalized ads to you.", "SKAdNetworkItems": [{"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}, {"SKAdNetworkIdentifier": "v9wttpbfk9.skadnetwork"}, {"SKAdNetworkIdentifier": "n38lu8286q.skadnetwork"}], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}, "appleTeamId": "A93LYUQ4VC", "buildNumber": "9"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive_icon.png", "backgroundColor": "#ffffff"}, "package": "rabid.app", "permissions": ["android.permission.RECORD_AUDIO", "android.permission.INTERNET", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION", "android.permission.RECORD_AUDIO", "android.permission.INTERNET", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"], "edgeToEdgeEnabled": true, "versionCode": 37}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-asset", {"assets": ["assets/images"]}], "expo-router", ["expo-splash-screen", {"image": "./assets/images/splash_icon.png", "imageWidth": 200, "resizeMode": "cover", "backgroundColor": "#0E100F"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.487589515794-rupa9ebkfks97c9htl1dtfn059tfeqfo"}], ["expo-apple-authentication"], ["react-native-fbsdk-next", {"appID": "1338626817399717", "clientToken": "b5987bafa8a8915347402efba88883f7", "displayName": "<PERSON><PERSON>", "scheme": "fb1338626817399717", "advertiserIDCollectionEnabled": false, "autoLogAppEventsEnabled": false, "isAutoInitEnabled": true, "iosUserTrackingPermission": "This identifier will be used to deliver personalized ads to you."}], "expo-asset", ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], ["react-native-edge-to-edge", {"android": {"parentTheme": "<PERSON><PERSON><PERSON>", "enforceNavigationBarContrast": false}}], ["expo-font", {"fonts": ["node_modules/@expo-google-fonts/inter/400Regular/Inter_400Regular.ttf", "node_modules/@expo-google-fonts/inter/500Medium/Inter_500Medium.ttf", "node_modules/@expo-google-fonts/inter/600SemiBold/Inter_600SemiBold.ttf", "node_modules/@expo-google-fonts/inter/700Bold/Inter_700Bold.ttf", "node_modules/@expo-google-fonts/inter/800ExtraBold/Inter_800ExtraBold.ttf", "node_modules/@expo-google-fonts/inter/900Black/Inter_900Black.ttf"]}], ["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0"}, "ios": {"deploymentTarget": "15.1", "ccacheEnabled": true}}]], "experiments": {"typedRoutes": true, "remoteBuildCache": {"provider": "eas"}}, "extra": {"router": {"origin": false}, "eas": {"projectId": "cf9626a2-eb7f-42ba-a278-3879dca8b99a"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/cf9626a2-eb7f-42ba-a278-3879dca8b99a"}, "assetBundlePatterns": ["assets/**/*"]}}